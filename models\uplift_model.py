#!/usr/bin/env python
# coding=utf8
import math
import numpy as np
import tensorflow as tf
from functools import partial
from collections import defaultdict
import sail.common as S
import sail.model as M
from sail import layers as L
from sail import layers
from sail import losses as Losses
from sail import initializers
from sail import optimizers
from sail import modules
from sail.feature import FeatureSlot
from sail.feature import FeatureColumnV1
from sail.feature import FeatureColumnV2
from sail.feature import FeatureColumn3D
from sail.feature import FeatureColumnDense
from sail import initializers

from feature import *
from custom_module import *
from multitask_utils import Target, grad_norm, relogit

MODEL_NAME = "tiktok_shopping_gmvmax_uplift_model"

SHARE_BOTTOM_DIM = [1024, 512, 256]
SHARE_BOTTOM_DIM_ACTIVATIONS = [L.<PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>()]

TARGET_BIAS_TOWER_DIM = [64, 32]
TARGET_BIAS_TOWER_DIM_ACTIVATIONS = [<PERSON><PERSON>(), <PERSON><PERSON>()]

TARGET_TOWER_DIM = [32, 1]
TARGET_TOWER_ACTIVATIONS = [<PERSON><PERSON><PERSON>(), None]


adamom_opt = optimizers.AdaMom(lr=0.000005, ada_decay=.9999, mom_decay=.99, init_factor=1.0)

GRAD_NORM = False  # 是否启用梯度归一化
DENSE_NEED_DROPOUT = True  # 是否对 Dense Features 应用 Dropout

OCCURRENCE_THRESHOLD = 7  # 特征槽的出现次数阈值
UUE_SCALE_INIT = 0.1
NEGATIVE_SAMPLE_RATE = 0.1  # 负样本采样率
FID_V1_MAX_SLOT_ID = 1023  # 特征槽 ID 的最大值
is_training = S.is_training()  # 是否处于训练模式

DEFAULT_EXPIRE_TIME = 30  # 特征槽的默认过期时间，对于特定的特征槽可以通过 expire_time_conf 设置过期时间
expire_time_conf = {
    7: [593, 545, 591, 590, 542, 592, 544],
    10: [2, 204, 2006, 205, 714, 4002, 3951, 2009, 4],
    30: [513, 719, 716, 717],
    60: [718, 542],
    2: [3795],
    90: [1],
    1: [1172,1245]
}

# TOP_SLOTS = [1, 87, 590, 542, 543, 544, 547, 545, 591, 592, 593, 611, 612, 1379, 2797, 2798, 3469, 3725, 3726, 3727, 3728, 3729, 3730, 3732, 3733, 3734, 3737, 3738, 3739, 3740, 3741, 3763, 3765, 3767, 3768, 3769, 3775, 3776, 3777, 3778, 3794, 3887, 3890, 3950, 4095, 4375, 4457, 4539, 4552, 4698, 4702, 4703, 4711, 8542, 8543, 8555, 8558]
TOP_SLOTS = [1, 87, 590, 542, 543, 544, 547, 545, 591, 592, 593, 611, 612, 1379, 2797, 2798]

def get_vec_alpha_with_hessian_sketching(slot_id):
    return 1.2 if slot_id in TOP_SLOTS else 1

def get_hessian_compression_times(slot_id):
    return 10 if slot_id in TOP_SLOTS else 1

def get_expire_time(slot_id):
    '''获取特定 slot 特征的过期时间'''
    expire_time_dict = {}
    for expire_time, slots in expire_time_conf.iteritems():
        for slot in slots:
            expire_time_dict[slot] = expire_time
    expire_time = expire_time_dict.get(slot_id, DEFAULT_EXPIRE_TIME)
    return expire_time

# use line_id to select first-initiate_order samples
# fc_first_action = FeatureColumnDense('fc_line_id_first_action', 1, tf.int64)
# first_action = fc_first_action.get_tensor()
# first_action = tf.reshape(first_action, [-1])

# 用户画像特征（240天，7天，30天）
BG_USER_PROFILE_240d = [1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375, 
                        1376, 1377, 1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1386, 1387, 1388, 1389, 1390, 1400, 
                        1401, 1402, 1403, 1404, 1405, 1406, 1407, 1408, 1412, 1413, 1414, 1418, 1419, 1420, 1434, 1435, 
                        1436, 1437, 1438, 1439, 1446, 1454, 1455, 1456, 1457, 1458, 1459, 1466, 1470, 1471, 1472, 1473, 
                        1474, 1475, 1476, 1477, 1478, 1479, 1483, 1486, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936]

BG_USER_PROFILE_7d = [1280, 1281, 1282, 1283, 1284, 1285, 1286, 1287, 1288, 1289, 1290, 1291, 1292, 1293, 1294, 1295, 
                      1296, 1297, 1298, 1299, 1300, 1301, 1302, 1303, 1304, 1305, 1306, 1307, 1317, 1318, 1319, 1320, 
                      1321, 1322, 1323, 1324, 1325, 1329, 1330, 1331, 1335, 1336, 1337, 1937, 1938, 1939, 1940, 1941, 
                      1942]

BG_USER_PROFILE_30d = [1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958]

# 参考特征（APP ID, Pixel ID）
REF_APP_ID_SLOTS = [1934, 1935, 1936, 1939, 1942, 1950, 1958]
REF_PIXEL_ID_SLOTS = [1931, 1932, 1933, 1938, 1941, 1949, 1957]

# 受众特征
AUDIENCE_SLOTS = [1001, 1002, 1003]

# 交互特征
INTERACT_SLOTS = [928, 929, 930, 847, 848, 931, 932, 933, 934]

# 像素特征
PIXEL_SLOTS_USER = [400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419,
                    420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 440, 441, 442, 443, 444,
                    445, 446, 447, 448, 449, 450, 452, 453, 455, 456, 457, 458, 459, 465, 466, 467, 480, 481, 482, 483,
                    484, 485, 486, 487, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 510, 511, 512, 514,
                    554, 556, 558, 559, 563, 564, 566, 568, 569, 598, 599, 619, 620, 621, 622, 623, 624, 625, 626, 627,
                    628, 629, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 655, 664, 665, 668,
                    669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 792, 793, 794]
PIXEL_SLOTS_AD = [287, 290, 691, 692, 693, 694, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 922, 923, 924]
PIXEL_V3_SLOTS = [1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980]

# 频率特征
FREQUENCY_SLOTS = [2761, 2791, 2794, 2795, 2796, 2797, 2798, 2800, 2801, 2804, 2807, 2810, 2846, 7042, 8503, 8509, 8510,
                   8513, 8514, 8515, 8527, 8528, 8529, 8530, 8531, 8533, 8534, 8535, 8536, 8537, 8538, 8539, 8540, 8541,
                   8542, 8543, 8545, 8546, 8547, 8548, 8549, 8550, 8551, 8552, 8553, 8554, 8555, 8557, 8558, 8559, 8560,
                   8561, 8562, 8563, 8564, 8565, 8566, 8567, 8569, 8570, 8571, 8572, 8573, 8574]

# 用户标签特征
HASH_TAG_SLOTS = [1527, 1528, 1537, 1538, 1539, 1540, 1541, 1542]

# 行业标签特征
NEW_LABEL_INDUSTRY_SLOTS = [2666, 2670, 2671, 2672, 2673, 2674, 2678, 2679, 2680, 2681, 2682, 2688, 2689, 2690, 2694,
                            2696, 2697, 2705, 2713, 2721, 2722, 2726, 2727, 2728, 2729, 2822, 2823, 2827, 2829, 2830,
                            2834, 2836, 2816]

NEW_LABEL_INDUSTRY_MATCH_SLOTS = [2746, 2752, 2755, 2758, 2761, 2764, 2788, 2837, 2843, 2846, 2849]

# 电商特征
ECOM_GROUP_SLOTS = [3725, 3726, 3727, 3728, 3729, 3730, 3731, 3732, 3733, 3734, 3735, 3736, 3737,
                    3738, 3739, 3740, 3741, 3742, 3743, 3794, 3796, 4095, 4457] # 3794 4457 duplicate

ECOM_SPU_GROUP_SLOTS = [3763, 3765, 4979, 4980, 4981, 4982, 3775, 3776, 3777, 3778] + [7914, 7927, 7935, 13470, 13471, 13575, 13576, 13578, 30101]

ECOM_MATCH_SLOTS = [4372, 4374, 4375, 4376, 4377, 4535, 4536, 4537, 4538, 4539, 4540, 4541, 4542, 4543, 4544, 4545, 
                    4548, 4551, 4552, 4553, 4554, 4555, 4556, 4557, 4687, 4688, 4689, 4690, 4691, 4692, 4693, 4694, 
                    4695, 4696, 4697, 4698, 4699, 4700, 4701, 4702, 4703, 4704, 4705, 4706, 4707, 4708, 4709, 4710, 
                    4711, 4712, 4713, 4714, 4715, 4716]

# 点赞特征
LIKE_SLOTS = [1967, 2336, 4128, 4139, 4256, 4324, 4327, 4332, 4900, 4903, 4907, 5301, 5456, 5462, 5467, 5742, 5864,
              5912, 5928, 5936, 5944, 5960, 5965, 5982, 5984, 6099, 6120, 6128, 6272, 6275, 6286, 6296, 6312, 6320,
              6328, 6368, 6507, 6520, 6525, 6528, 6568, 6600, 6640, 6712, 6776, 6808, 
              6184, 4886] # from lsa

# 搜索特征
SEARCH_FEA_SLOTS = [11236, 11237, 11238, 11245]

# 高价值特征
HIGH_JS_DIV_NON_BUSINESS = [3251, 3260, 3261, 3262, 3263, 8005]
HIGH_JS_DIV_BUSINESS = [1111, 1112, 1113, 1701, 1740, 1776, 1777, 1778, 1920, 1921, 3434, 7017, 7018, 7019, 7020]

SIMIDV3_MATCH = [17009, 17011, 17007, 17005, 13354, 16877, 13356, 13358, 16884, 13361, 13363, 16879, 13367, 13369, 16882]

# ECOM_SLOTS = [3950, 3953, 3954, 3955, 4097, 4166, 4199, 4226, 3887, 3890, 3891, 3892, 5892, 6780, 6824, 6832]
# ECOM_ID_SLOTS = [3951, 3957, 3958, 3959, 3960, 3961, 4228, 4229, 4230, 4231, 4098, 3888, 3894, 3895, 3896, 3897, 6308, 6888]

ECOM_SHORT_MATCH_AND_LAST_ACTION_TIME_SLOTS = [
    14509, 14502, 14496, 14485, 14497, 14486, 14498, 14487, 14510, 14507, 14506, 14495, 14490, 14476, 14491, 14477, 14492,
    14478, 14508, 14501, 14499, 14488, 14479, 14470, 14481, 14472, 14483, 14474, 14503, 14493, 14500, 14489, 14480, 14471,
    14482, 14473, 14484, 14475, 14505, 14494, 14466, 14453, 14455, 14458, 14463, 14446, 14449, 14451, 14468, 14459, 14460,
    14461, 14469, 14465, 14452, 14454, 14457, 14467, 14462, 14445, 14447, 14450, 14464]

EXTRA_USER_PROFILE = [2783, 2784, 2785, 5675, 6027]
CLM_USER_PROFILE_II = [21803, 21804, 21805, 21806, 21807, 21809, 21810, 21811]
CLM_USER_PROFILE_III = [22781, 22782, 22783, 22784, 22785, 22786, 22787, 22788, 22789, 
                        22790, 22791, 22792, 22793, 22794, 22795, 22796, 22797, 22798]

ADD_PRODUCT_PROFILE_SLOTS = [8265, 7856, 8459, 8460, 8461, 8462, 8463, 8464, 8465, 8836, 8837, 10296, 10297, 10298, 10299, 7902,
                             7907, 7911, 7912, 7914, 7915, 7917, 7920, 7922, 7927, 7928, 7930, 7931, 7932, 7935, 7941, 7943, 7944,
                             7948, 7953, 7954, 7955, 7956, 7957, 7958, 7959, 7960, 7961, 7962, 7963, 7964, 7965, 7966, 7967, 7968,
                             7969]
ADD_PRODUCT_PROFILE_COUNT_SLOTS = [7900, 7901, 7903, 7904, 7905, 7906, 7908, 7909, 7910, 7913, 7916, 7918, 7919, 7921, 7923,
                                   7924, 7925, 7926, 7929, 7933, 7934, 7936, 7937, 7938, 7939, 7940, 7942, 7945, 7946, 7947,
                                   7949, 7950, 7951, 7952]

ADDED_SLOTS = [51, 52, 54, 76, 77, 78, 79, 90, 91, 92, 93, 96, 97, 98, 99, 325]

####from lsa####
native_slots = [1200, 1201, 1202, 75]
new_play_list_slots = [2880, 2881, 2882, 2883, 2894, 2895, 2896, 2897, 2898, 2906, 2908, 2909, 2910, 2911,
                       2920, 2922, 2923, 2925] 
match_cnt_slots = [2794, 2795, 2796, 2797, 2798, ] + [1283, 8503, 8506, 8527, 8528, 8529, 8530, 8531, 8539, 8540, 8541,
                                                      8542, 8543, 8551, 8552, 8553, 8554, 8555, 8563, 8564, 8565, 8566,
                                                      8567, 8570, 7033] # 7033 lsa only 
new_interact_slots = [1920, 1921, 1925, 1927, 1945, 1946, 1954]
shop_ad_features = [1651, 3794, 3795, 3796, 13470] #3796
fe_material_id_slot = [4337]
live_g_basic_profile = [8731, 8732, 8733, 8734, 8735, 8736, 8737, 8738, 8739, 8740, 8741, 8742, 8743, 8744, 8745, 8746, 8747]
live_g_statistical_profile = [8942, 8941, 8940, 8939, 8938, 8937, 8936, 8748]
live_g_rate_profile = [8950, 8948, 8946, 8944, 8943, 8951, 8949, 8947, 8945]
live_g_bucket_profile = [9197, 9196, 9195, 9194, 9193, 9192, 9191, 9190, 9189, 3279]
live_g_all = live_g_basic_profile + live_g_rate_profile + live_g_statistical_profile + live_g_bucket_profile
live_rec_bg_user_profile = [4603, 4600, 4599, 4598, 4597, 4596, 4582, 4581, 4580, 4579, 4574, 4573, 4572, 4571, 4570]
live_rec_bg_user_profile_match_cnt = [4641, 4640, 4639, 4638, 4637, 4636, 4635, 4634, 4633, 4632, 4631, 4630, 4629, 4628, 4627, 4626, 4625, 4624, 4623, 4622, 4621, 4620, 4619, 4618, 4617, 4616, 4615, 4614, 4613, 4612, 4611, 4610, 4609, 4608, 4607, 4606, 4595, 4594, 4593, 4592, 4591, 4590, 4589, 4588, 4587, 4586, 4585, 4584, 4583]
####from lsa####

### for seq 
target_slots = [2705, 2713, 2721, 2681, 2689, 2697] + [900, 87] + shop_ad_features + fe_material_id_slot + [3777, 3775, 4979, 4980, 4981, 4982, ]
### for seq

# 有效特征
VALID_SLOTS = [1, 2, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
               33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 50, 53, 55, 56, 70, 71, 85, 86, 87, 89, 94, 95, 115, 117,
               118, 119, 143, 144, 145, 147, 148, 154, 160, 161, 162, 163, 164, 165, 169, 170, 173, 174, 190, 192,
               193, 194, 195, 200, 202, 203, 204, 205, 206, 207, 208, 214, 215, 299, 315, 316, 317, 318, 329, 330,
               338, 339, 365, 381, 382, 451, 454, 471, 473, 474, 476, 489, 515, 516, 517, 518, 555, 560, 561, 562, 
               567, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 710, 711, 712, 713, 714,
               715, 740, 741, 742, 743, 744, 745, 750, 751, 752, 753, 756, 757, 758, 759, 762, 763,
               764, 765, 766, 767, 769, 770, 771, 772, 790, 791, 810, 811, 812, 900, 920, 921, 947, 950, 951, 952,
               953, 955, 958, 960, 969,
               971, 10449] + BG_USER_PROFILE_240d + BG_USER_PROFILE_7d + BG_USER_PROFILE_30d + REF_APP_ID_SLOTS + \
              REF_PIXEL_ID_SLOTS + AUDIENCE_SLOTS + INTERACT_SLOTS + PIXEL_SLOTS_USER + PIXEL_SLOTS_AD + \
              PIXEL_V3_SLOTS + FREQUENCY_SLOTS + ADDED_SLOTS + \
              HASH_TAG_SLOTS + NEW_LABEL_INDUSTRY_SLOTS + NEW_LABEL_INDUSTRY_MATCH_SLOTS + \
              ECOM_GROUP_SLOTS + ECOM_SPU_GROUP_SLOTS + [4453] + ECOM_MATCH_SLOTS + \
              [4302] + [1651, 15000] + LIKE_SLOTS + SEARCH_FEA_SLOTS + HIGH_JS_DIV_NON_BUSINESS + HIGH_JS_DIV_BUSINESS + \
              [542, 543, 544, 545, 590, 591, 592, 593, 595, 547, 720, 721, 722, 723] + \
              SIMIDV3_MATCH + ECOM_SHORT_MATCH_AND_LAST_ACTION_TIME_SLOTS + \
              EXTRA_USER_PROFILE + CLM_USER_PROFILE_II + CLM_USER_PROFILE_III + \
              ADD_PRODUCT_PROFILE_SLOTS + ADD_PRODUCT_PROFILE_COUNT_SLOTS + [1741, 1742] + \
              native_slots + new_play_list_slots + match_cnt_slots + new_interact_slots + \
              shop_ad_features + fe_material_id_slot + [5403, ] + \
              live_g_all + live_rec_bg_user_profile + live_rec_bg_user_profile_match_cnt + target_slots

# 广告类别特征
AD_CATEGORY_SLOTS = [8, 28, 29, 33, 34, 35, 55, 70, 117, 118, 119, 148, 160, 161, 162, 190, 192, 193, 194, 195, 203,
                     214, 215, 338, 339, 471, 473, 603, 604, 605, 743, 744, 745, 769, 810, 811, 812, 953, 955]

ALL_EMB_GROUP_FEATURE = [13, 70, 381, 382, 740, 85, 86, 87, 89, 94, 4, 7, 365]

# 废弃特征
DEPRECATED_SLOTS = [1972,1977,1974,1979,1973,1978,1975,1980,1971,1976,1367,1379,1436,1381,1483,1476,1371,1374,1380,1456,8530,8534,8528,8529,8531,8533,8536,8535,8506,8514,8510,8513,2791,2761,1284,1296,7042,1298,1294,1288,1291,1297,8543,8555,8559,8564,8565,8567,8572,8571,2798,8742,8741,4582,4595,4588,3251,3261,3263,3260,3262,4641,4628,4615] + [1,2,1284,1285,1286,1287,1288,1289,8552,2795,8540,8528,8564,1367,1368,1369,1370,1371,1372]

# 最终生效特征
VALID_SLOTS = list(set(VALID_SLOTS) - set(AD_CATEGORY_SLOTS) - set(DEPRECATED_SLOTS))

# 偏置特征（仅学习偏置项）
BIAS_ONLY_SLOTS = [6, 16, 17, 25, 31, 32, 37, 41, 43, 56, 86, 117, 118, 119, 143, 144, 145, 147, 148, 190, 192, 193, 194, 195, 200, 202, 203, 206, 207, 214, 215, 338, 339, 353, 355, 410, 411, 412, 413, 414, 417, 418, 419, 420, 421, 422, 424, 427, 429, 431, 432, 433, 434, 441, 442, 444, 445, 446, 447, 448, 449, 454, 455, 458, 459, 467, 487, 493, 494, 496, 497, 498, 499, 502, 510, 512, 554, 555, 558, 559, 560, 561, 562, 567, 607, 619, 620, 621, 625, 628, 629, 636, 644, 645, 655, 665, 668, 669, 670, 672, 674, 676, 679, 680, 681, 682, 683, 741, 742, 743, 744, 745, 750, 751, 752, 753, 756, 757, 758, 759, 762, 763, 764, 765, 769, 770, 771, 772, 793, 794, 910, 911, 914, 915, 916, 917, 918, 919, 922, 923, 1285, 1287, 1296, 1297, 1299, 1300, 1302, 1303, 1304, 1306, 1307, 1367, 1385, 1386, 1387, 1389, 1390, 1435, 1455, 1470, 1471, 1472, 1475, 7033] +NEW_LABEL_INDUSTRY_MATCH_SLOTS + FREQUENCY_SLOTS + match_cnt_slots + live_rec_bg_user_profile_match_cnt
BIAS_ONLY_SLOTS = set(BIAS_ONLY_SLOTS) - set(DEPRECATED_SLOTS)

# FM 特征
FM_USER1 = [5, 9, 11, 12, 15, 21, 23, 24, 27, 44, 173, 330]
FM_GROUP1 = [900, 365, 71, 4, 115, 299, 489] + AUDIENCE_SLOTS + shop_ad_features

FM_USER5 = INTERACT_SLOTS
FM_GROUP5 = [900, 365, 4, 115, 299] + AUDIENCE_SLOTS + shop_ad_features

# 主函数（前面都是常量定义）
def generate_model(model_name):
    """Generate the deep model structure
    Args:
        model_name: prefix name of the output model file
    """
    assert model_name, 'model name could not be emtpy or None'

    # Set defaults for FeatureColumns
    FeatureSlot.set_default_occurrence_threshold(OCCURRENCE_THRESHOLD)  # 特征 slot 出现次数阈值
    FeatureSlot.set_default_expire_time(DEFAULT_EXPIRE_TIME)  # 特征 slot 过期时间

    # Set defaults for optimizers（FTRL: 在线学习优化算法）
    default_bias_slot_bias_opt = optimizers.FTRL(alpha=0.01, beta=1.0, lambda1=1.0, lambda2=1.0)
    default_vec_slot_bias_opt = optimizers.FTRL(alpha=0.01, beta=1.0, lambda1=1.0, lambda2=1.0)
    default_vec_slot_vec_opt = optimizers.AdaGrad(alpha=0.025, weight_decay=0.001, beta=1.0, init_factor=0.0625)
    default_variablle_opt = optimizers.AdaGrad(alpha=0.01, weight_decay=0.0, beta=1.0, init_factor=1)

    # allocate feature columns
    input_map = {}  # {slot_id: input_tensor}
    fc_dict = {}  # {slot_id: FeatureColumn}
    for slot in sorted(VALID_SLOTS):
        expire_time = get_expire_time(slot)
        if slot in BIAS_ONLY_SLOTS:
            fs = FeatureSlot(slot_id=slot,
                             bias_optimizer=default_bias_slot_bias_opt,
                             occurrence_threshold=OCCURRENCE_THRESHOLD,
                             expire_time=expire_time)
        else:
            fs = FeatureSlot(slot_id=slot,
                             vec_optimizer=optimizers.FTRLWithGroupSparsity(alpha=0.025 * get_vec_alpha_with_hessian_sketching(slot),  # 对重要特征进行学习率调整
                                                                            beta=1.0, 
                                                                            init_factor=0.0625,
                                                                            lambda1=1,
                                                                            lambda2=1,
                                                                            hessian_compression_times=get_hessian_compression_times(slot),  # 对重要特征增加压缩次数
                                                                            zero_val_expire_time=3),
                             bias_optimizer=default_bias_slot_bias_opt,
                             occurrence_threshold=OCCURRENCE_THRESHOLD,
                             expire_time=expire_time)
        # 判断特征 ID 版本：slot ID 大于 FID_V1_MAX_SLOT_ID 则为 V2 版本，否则为 V1 版本
        fc_name = ""
        if slot > FID_V1_MAX_SLOT_ID:
            if slot not in FC_ID_NAME_DICT:
                raise Exception("fc slot has not fc_name! slot_id = %d" % slot)
            fc_name = FC_ID_NAME_DICT[slot]
        if slot in fc_dict:
            raise Exception("fc_dict has slot_id = %d !" % slot)
        if slot > FID_V1_MAX_SLOT_ID:
            fc_dict[slot] = FeatureColumnV2(fc_name, fs)
        else:
            fc_dict[slot] = FeatureColumnV1(fs)

    # shared
    # for fc_name, slot in CLM_USER_PROFILE.items():
    #     expire_time = get_expire_time(slot)
    #     if slot not in fs_dict:
    #         fs_dict[slot] = FeatureSlot(slot_id=slot,
    #                                 vec_optimizer=optimizers.FTRLWithGroupSparsity(
    #                                     alpha=0.025 * get_vec_alpha_with_hessian_sketching(slot),
    #                                     beta=1.0,
    #                                     init_factor=0.0625,
    #                                     lambda1=1,
    #                                     lambda2=1,
    #                                     hessian_compression_times=get_hessian_compression_times(slot),
    #                                     zero_val_expire_time=3),
    #                                 bias_optimizer=default_bias_slot_bias_opt,
    #                                 occurrence_threshold=OCCURRENCE_THRESHOLD,
    #                                 expire_time=expire_time)
    #     fs = fs_dict[slot]

    #     fc_dict[fc_name] = FeatureColumnV2(fc_name, fs)

    # LR part（偏置特征）
    bias_input = M.get_bias([fc_dict[sid] for sid in VALID_SLOTS])
    bias_output = tf.reduce_sum(bias_input, axis=1)
    print("bias_input: ",bias_input)
    tf.summary.histogram('bias_output', bias_output)

    # FM part（交叉特征）
    total_embeddings = []

    user_pooling_1 = sum_pooling(fc_dict, input_map, FM_USER1, 64, total_embeddings)
    group_pooling_1 = sum_pooling(fc_dict, input_map, FM_GROUP1, 64, total_embeddings)
    user_pooling_5 = sum_pooling(fc_dict, input_map, FM_USER5, 16, total_embeddings)
    group_pooling_5 = sum_pooling(fc_dict, input_map, FM_GROUP5, 16, total_embeddings)

    fms = [
        tf.multiply(user_pooling_1, group_pooling_1),
        tf.multiply(user_pooling_5, group_pooling_5),
    ]
    fm_outputs = [tf.reduce_sum(fm, axis=1) for fm in fms]
    
    fm_cross_ue_slot = [900, 365, 4, 115, 299] + [3794, 4337, 13470]

    # 引入外部交叉
    gen_ue('fc_tiktok_ue_uid_472510', 128, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=True,
            need_compress=False, compress_dims=[128, 64],
            need_fm=True, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True, need_compress_from_raw=False
            )
    # 推荐侧rgid UE: https://bytedance.feishu.cn/docx/P2HOd0dlcoB9S6xNACmcRiNfnqd
    gen_ue('fc_muse_mtl_user_embedding_335016_d8', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[128, 64],
            need_fm=True, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True, need_compress_from_raw=False
            )

    # 商品图文信息embedding: https://bytedance.feishu.cn/docx/W5jcdZXCjoY8MyxL5UEcm9dAnrf
    # gen_ue('fc_ecom_spu_id_ue_128d', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
    #         need_dropout=False,
    #         need_compress=True, compress_dims=[128, 64],
    #         need_fm=False, fm_cross_slot=[1],
    #         need_concat_nn=True, need_compress_from_raw=True
    #         )

    gen_ue('fc_live_mt_predict_ctr_user_fid_d64_test', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[64, 32],
            need_fm=False, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True
            )

    gen_ue('fc_live_mt_predict_staytime_user_fid_d64_test', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[64, 32],
            need_fm=False, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True
            )

    gen_ue('fc_live_mt_predict_interaction_user_fid_d64_test', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[64, 32],
            need_fm=False, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True
            )

    gen_ue('fc_live_mt_predict_interaction_author_fid_d64_test', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[64, 32],
            need_fm=False, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True
            )

    gen_ue('fc_live_mt_predict_staytime_author_fid_d64_test', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[64, 32],
            need_fm=False, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True
            )

    gen_ue('fc_live_mt_predict_ctr_author_fid_d64_test', 64, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False,
            need_compress=True, compress_dims=[64, 32],
            need_fm=False, fm_cross_slot=fm_cross_ue_slot,
            need_concat_nn=True
            )
    
    fm_concat = tf.concat(fms, axis=1)
    fm_output = tf.add_n(fm_outputs)
    print("fm_concat: ",fm_concat)
    for i, fm in enumerate(fms):
        tf.summary.histogram('fm{}_output'.format(i), fm)
    tf.summary.histogram('fm_output', fm_output)

    # all embedding（处理不在 input_map 的其他特征，有 16 维的和 32 维的）
    all_embedding_slots = set(VALID_SLOTS) - set(BIAS_ONLY_SLOTS)
    for slot in sorted(all_embedding_slots):
        if not slot in input_map:  
            if not slot in ALL_EMB_GROUP_FEATURE:
                if slot > FID_V1_MAX_SLOT_ID:
                    embedding = fc_dict[slot].get_vector(fc_dict[slot].feature_slot.add_slice(16))
                    total_embeddings.append((embedding, 16))
                else:
                    embedding = fc_dict[slot].add_vector(16)  # 生成 16 维 embedding
                    total_embeddings.append((embedding, 16))
            else:
                # group feature（组特征，信息更丰富，保留 32 维）
                embedding = fc_dict[slot].add_vector(32)
                total_embeddings.append((embedding, 32))
            
            debug_emb_coverage_summary(embedding, "allemb_slot_" + str(slot))

            # 这里增加特征筛选逻辑

    # seq feature
    init = initializers.VarianceScaling(scale=1.0, mode='fan_avg', distribution='uniform')  # 参数初始化（方差缩放）

    # 用户序列特征
    target_dim = 32 * 16 + 16 + 16 * 16 + 16
    # target_input = get_embedding_input(fc_dict, target_slots, 32)
    # target_out = modules.DenseTower(name="SEQ_TARGET_TOWER",
    #                                 output_dims=[256, 256, target_dim],
    #                                 activations=layers.Relu(),
    #                                 initializers=init,
    #                                 optimizers=optimizers.AdaMom(
    #                                     lr=0.000005, ada_decay=.9999, mom_decay=.99, init_factor=0.05)
    #                                 )(target_input)

    # seq_browser = do_seq(fc_dict, ECOM_SEQ_FC_NAME, target_out, ECOM_SEQ_ID_365d_browser, ECOM_SEQ_PROFILE_365d_browser, 30)
    # seq_cart = do_seq(fc_dict, ECOM_SEQ_FC_NAME, target_out, ECOM_SEQ_ID_365d_cart, ECOM_SEQ_PROFILE_365d_cart, 30)
    # seq_purchase = do_seq(fc_dict, ECOM_SEQ_FC_NAME, target_out, ECOM_SEQ_ID_365d_purchase, ECOM_SEQ_PROFILE_365d_purchase, 30)
    # seq_like = do_seq(fc_dict, ECOM_SEQ_FC_NAME, target_out, ECOM_SEQ_ID_365d_like, ECOM_SEQ_PROFILE_365d_like, 30)
    # print("seq_browser: ",seq_browser)
    # print("seq_cart: ",seq_cart)
    # print("seq_purchase: ",seq_purchase)
    # print("seq_like: ",seq_like)

    #直播间序列特征
    # query_dim = 64
    # query_input = get_shared_embedding_input(fc_dict, room_spu_query_slots, 32, {})
    # print("query_input:",query_input.shape)
    # query_out = modules.DenseTower(name="SEQ_QUERY_TOWER",
    #                                 output_dims=[128, query_dim],
    #                                 activations=layers.Relu(),
    #                                 initializers=init,
    #                                 optimizers=optimizers.AdaMom(
    #                                     lr=0.000005, ada_decay=.9999, mom_decay=.99, init_factor=0.05)
    #                                 )(query_input)
    # seq_spu = do_seq_din(fc_dict, query_out, room_spu_key_slots, room_spu_value_slots, seq_len=30, slot_dim=32, query_emb_dim=query_dim, attention_emb_dim=32, name='Room_Seq_Spu')
    # seq_spu = do_seq(fc_dict, ROOM_SEQ_FC_NAME, query_out, room_spu_key_slots, [], 30)
    # seq_spu = get_shared_fc_with_sumpooling(fc_dict, room_spu_key_slots, ROOM_SEQ_FC_NAME, slice_index=0)
    # seq_spu = tf.concat(seq_spu,axis=1)
    # print("Seq_spu: ",seq_spu)
    # seq_spu = tf.reduce_sum(xd_tensor,axis=1)

    # # ========== dense feature start ========== 
    # DENSE_DIM = 4
    # DENSE_BUCKET_NUM = 10
    # dense_feature_list = []
    # DENSE_FEA_DICT = {}

    # for name in DENSE_FC_CONFIG:
    #     max_size, lower_bound, upper_bound, fun, bucket_num = DENSE_FC_CONFIG[name]
    #     if name in FLOAT_DENSE_FC:
    #         dense_feature = tf.cast(FeatureColumnDense(name, max_size,tf.float64).get_tensor(), tf.float64)
    #     else:
    #         dense_feature = tf.cast(FeatureColumnDense(name, max_size,tf.int64).get_tensor(), tf.float64)
    #     tf.summary.histogram('dense_feature_ori/{}'.format(name), dense_feature)
    #     dense = tf.reshape(dense_feature,[-1])
    #     DENSE_FEA_DICT[name] = dense
    #     f_id = div2bucket(dense, lower_bound, upper_bound, fun == 'log', bucket_num)
    #     fc_dense_embedding = get_embedding("dense/"+name,f_id,DENSE_DIM,bucket_num)
    #     # dropout
    #     if is_training and DENSE_NEED_DROPOUT:
    #         fc_dense_embedding = dense_dropout(fc_dense_embedding,name)
    #     dense_feature_list.append(fc_dense_embedding)
    # print("dense feature nums: ",len(dense_feature_list))
    # dense_feature_list = tf.concat(dense_feature_list, axis=1)
    # print("dense feature: ",dense_feature_list)

    # dense_user_pay_fea = []
    # for name in DENSE_CALC_FC_CONFIG:
    #     cnt_fea_name, gmv_fea_name, lower_bound, upper_bound, fun, bucket_num = DENSE_CALC_FC_CONFIG[name]
    #     cnt_fea = DENSE_FEA_DICT[cnt_fea_name]
    #     gmv_fea = DENSE_FEA_DICT[gmv_fea_name]
    #     avg_fea = tf.divide(gmv_fea, cnt_fea+0.1)
    #     avg_fea = tf.where(cnt_fea>0.5, avg_fea, tf.zeros_like(avg_fea))
    #     f_id = div2bucket(avg_fea, lower_bound, upper_bound, fun == 'log', bucket_num)
    #     fc_dense_embedding = get_embedding("dense/"+name,f_id,DENSE_DIM,bucket_num)
    #     dense_user_pay_fea.append(fc_dense_embedding)
    # print("avg_user_pay dense feature nums: ",len(dense_user_pay_fea))
    # dense_user_pay_fea = tf.concat(dense_user_pay_fea, axis=1)
    # print("avg_user_pay dense feature: ",dense_user_pay_fea)

    # dense_vedio_prd_fea = get_dense_list_feature('vedio', PRODUCT_VEDIO_DENSE_FC_NAME, 4, input_tf_type=tf.float64, is_dense_list=True)
    # dense_live_prd_fea = get_dense_list_feature('live', PRODUCT_LIVE_DENSE_FC_NAME, 4, input_tf_type=tf.float64, is_dense_list=True)

    # # ========== dense feature end ========== 

    # deep tower
    concat_dim = 0
    embeddings = [] 
    # 遍历 total_embeddings，拼接所有的 embedding 并累加 dim
    for embedding, dim in total_embeddings:
        embeddings.append(embedding)
        concat_dim += dim
    print('total_embeddings concat_dim=%d' % concat_dim)
    concat_embedding = tf.concat(embeddings, axis=1)  # 拼接所有的 embedding 特征，axis = 1 表示在每个样本上拼接特征维度

    # bottom net  Attetion, don't forget add new features here
    # bottom_input = tf.concat([bias_input, fm_concat, concat_embedding, seq_browser, seq_cart, seq_purchase, seq_like], axis=1)
    bottom_input = tf.concat([bias_input, fm_concat, concat_embedding], axis=1)  # 拼接 [偏置特征, 交叉特征, 其他特征]，作为底层网络最终输入
    print('bottom_input_dim = %d' % bottom_input.shape[1])

    # main_tower_ln_layer = L.LayerNorm(name='main_tower_ln')
    # with S.serving_in_float32():
    #     bottom_input = main_tower_ln_layer(bottom_input)

    # 全连接层 [1024, 512, 256]
    bottom_output = modules.DenseTower(name="BOTTOM_TOWER",
                                       output_dims=SHARE_BOTTOM_DIM,
                                       activations=layers.Relu(),
                                       initializers=init,
                                       optimizers=adamom_opt
                                       )(bottom_input)

    sample_rate = M.get_sample_rate()  # 获取采样率
    sample_bias = M.get_sample_bias()  # 获取采样偏置

    # pvr from line_id
    ori_cvr = tf.reshape(FeatureColumnDense('fc_line_id_ori_cvr', 1, tf.float32).get_tensor(), [-1])
    ori_ctr = tf.reshape(FeatureColumnDense('fc_line_id_ori_ctr', 1, tf.float32).get_tensor(), [-1])
    fc_pvr = ori_cvr * ori_ctr
    # fc_sample_pvr = fc_pvr / (fc_pvr + (1-fc_pvr)*NEGATIVE_SAMPLE_RATE)
    tf.summary.histogram('ori_pvr', fc_pvr)  # 记录 PVR 的分布到 TensorBoard，用于观察分布
    # tf.summary.histogram('sample_pvr', fc_sample_pvr)

    fc_pvr_mask_zero = tf.cast(tf.math.not_equal(fc_pvr, 0.0), tf.float32)  # 掩码标记 PVR != 0 的样本
    sample_pvr_logit = relogit(fc_pvr)  # 将 PVR 转换为 logit 形式
    tf.summary.histogram('sample_pvr_logit', sample_pvr_logit)
    tf.summary.histogram('pvr_mask_zero', fc_pvr_mask_zero)

    # add train and predict step
    q_data = M.Dataflow(name='q_data')
    r_data = M.Dataflow(name='r_data')
    
    q_data.add_label(label_name='label', label_idx=0)
    r_data.add_label(label_name='label', label_idx=0)

    run_predict = M.RunStep(name='predict_online', run_type='INFERENCE')
    run_q_train = M.RunStep(name='run_q_train', run_type='TRAIN', data_flow=q_data)
    run_r_train = M.RunStep(name='run_r_train', run_type='TRAIN', data_flow=r_data)

    tower_init = initializers.VarianceScaling(scale=1.0, mode='fan_avg', distribution='uniform')  # 任务塔参数初始化

    def create_task(head_name, label, use_residual=False, logit_infer=False):
        """Create a head for calculating q or r"""
        deep_out = modules.DenseTower(name="DEEP_TOWER_" + head_name,
                                      output_dims=TARGET_TOWER_DIM,
                                      activations=layers.Relu(),
                                      initializers=tower_init,
                                      optimizers=adamom_opt)(bottom_output)  # 输入为共享底部塔的输出 bottom_output，形状为 [batch_size, 256]
        bias_out = modules.DenseTower(name="BIAS_TOWER_" + head_name,
                                      output_dims=[1],
                                      activations=layers.Relu(),
                                      initializers=tower_init,
                                      optimizers=adamom_opt)(bias_input)  # 输入为 LR 部分的偏置特征 bias_input，形状为 [batch_size, 1]
        deep_out = tf.reduce_sum(deep_out, axis=1)  # 按 axis=1 求和，降低维度至 [batch_size]
        bias_out = tf.reduce_sum(bias_out, axis=1)
        logit = tf.add_n([deep_out, bias_out])  # logit = deep_out + bias_out，形状为 [batch_size]
        tf.summary.histogram(head_name + '_deep_out', deep_out)
        tf.summary.histogram(head_name + '_bias_out', bias_out)
        tf.summary.histogram(head_name + '_logit', logit)
        
        final_logit = logit
        # R 任务引入 ori_pvr，进行残差学习
        if use_residual:
            final_logit = tf.add_n([logit, sample_pvr_logit])  # 如果是 residual 任务，fianl_logit = logit + sample_pvr_logit（R 任务）
        final_logit = tf.clip_by_value(final_logit, -15, 15)
        tf.summary.histogram(head_name + '_final_logit', final_logit)
        # window concat, no sample bias
        # 获取负采样偏差后的 logit（这里有数学推导）
        sample_logit = get_sample_logits(final_logit, 
                                         NEGATIVE_SAMPLE_RATE * tf.ones_like(sample_rate, dtype=sample_rate.dtype),  # 负采样率，形状是 [batch_size]
                                         tf.zeros_like(sample_bias, dtype=sample_bias.dtype))  # 额外偏差修正，这里置零
        
        pred_pvr = tf.sigmoid(sample_logit, name=head_name + "_pred")
        tf.summary.histogram(head_name + '_pred_pvr', pred_pvr)
        
        if use_residual:
            loss = tf.reduce_sum(tf.nn.sigmoid_cross_entropy_with_logits(labels=label, logits=sample_logit) * fc_pvr_mask_zero)  # 仅对 ori_pvr != 0 的样本计算损失
        else:
            loss = tf.reduce_sum(tf.nn.sigmoid_cross_entropy_with_logits(labels=label, logits=sample_logit))
        tf.summary.scalar(head_name + '_loss', loss)
        
        return logit, final_logit, pred_pvr, loss

    q_label = q_data.get_label('label')
    r_label = r_data.get_label('label')
    q_label = tf.clip_by_value(q_label, 0, 1)    
    r_label = tf.clip_by_value(r_label, 0, 1)

    q_logit, q_final_logit, q_pred_pvr, q_pvr_loss = create_task('q', q_label, use_residual=False)
    r_residual_logit, r_final_logit, r_pred_pvr, r_pvr_loss = create_task('r', r_label, use_residual=True)

    ########### q train step ###########
    run_q_train.add_head(name='q_pvr_head', prediction=q_pred_pvr, label=q_label, loss=q_pvr_loss)  # q 任务训练
    run_q_train.add_head(name='r_pvr_in_q', prediction=r_pred_pvr, label=q_label, loss=tf.zeros_like(q_pvr_loss, dtype=q_pvr_loss.dtype))  # r 任务在 q 标签上的预测
    run_q_train.add_head(name='true_q_pvr', prediction=tf.sigmoid(q_final_logit), label=q_label, loss=tf.zeros_like(q_pvr_loss, dtype=q_pvr_loss.dtype))  # 真实 q_pvr
    run_q_train.add_head(name='true_r_pvr_in_q', prediction=tf.sigmoid(r_final_logit), label=q_label, loss=tf.zeros_like(q_pvr_loss, dtype=q_pvr_loss.dtype))  # 真实 r_pvr 在 q 标签上的预测
    run_q_train.add_loss(loss=q_pvr_loss)

    run_predict.add_head(name='q_pvr_head', prediction=tf.sigmoid(q_final_logit))  # 模型预估 q_pvr
    ########### q train step ###########

    ########### r train step ###########
    run_r_train.add_head(name='r_pvr_head', prediction=r_pred_pvr, label=r_label, loss=r_pvr_loss)
    run_r_train.add_head(name='q_pvr_in_r', prediction=q_pred_pvr, label=r_label, loss=tf.zeros_like(r_pvr_loss, dtype=r_pvr_loss.dtype))
    run_r_train.add_head(name='true_r_pvr', prediction=tf.sigmoid(r_final_logit), label=r_label, loss=tf.zeros_like(r_pvr_loss, dtype=r_pvr_loss.dtype))
    run_r_train.add_head(name='true_q_pvr_in_r', prediction=tf.sigmoid(q_final_logit), label=r_label, loss=tf.zeros_like(r_pvr_loss, dtype=r_pvr_loss.dtype))
    run_r_train.add_loss(loss=r_pvr_loss)

    run_predict.add_head(name='r_residual_head', prediction=r_residual_logit)  # 模型预估 r_residual_logit
    ########### r train step ###########

    # 各个 Head 任务的作用（以 q 任务为例，r 任务类似）：
    # q_pvr_head: 负样本采样后的 q 数据流走 q 任务塔，用于训练
    # q_pvr_in_r: 负样本采样后的 q 数据流走 r 任务塔，用于评估 q 和 r 两个任务塔的参数分布是否接近
    # true_q_pvr: 原始 q 数据流走 q 任务塔，用于计算真实 q_pvr
    # true_r_pvr_in_q: 原始 q 数据流走 r 任务塔，用于评估 q 和 r 两个任务塔的参数分布是否接近
    
    # 各个 Head 的输出均值记录
    # run_q_train: {q_pvr_head: 0.00371, r_pvr_in_q: 0.01075, true_q_pvr: 0.00048, true_r_pvr_in_q: 0.00133}
    # run_r_train: {r_pvr_head: 0.01109, q_pvr_in_r: 0.00411, true_r_pvr: 0.00141, true_q_pvr_in_r: 0.00053}

    # q_pvr_head 的输出应该是采样后的 q_pvr, true_q_pvr 的输出应该是基于原始数据分布（采样前）的 q_pvr
    
    tf.summary.merge_all()

    run_predict.add_feeds(M.get_all_input_feature_columns()) 
    run_q_train.add_feeds(M.get_all_input_feature_columns())
    run_r_train.add_feeds(M.get_all_input_feature_columns())
    # Assign RunSteps for training

    M.set_global_gradient_clip_norm(500.0)
    M.set_use_filter(False)
    # compile the whole model for training
    M.compile(default_hidden_layer_optimizer=adamom_opt,
              run_steps=[run_q_train, run_r_train, run_predict],
              # run_shopify_train
              num_estimated_bias_features=60000000,
              num_estimated_vec_features=500000,
              cold_feature_filter_capacity=300000000)


if __name__ == '__main__':
    """please set an appropriate model name"""
    model_name = MODEL_NAME
    generate_model(model_name)