# -*- encoding=utf-8 -*-

import tensorflow as tf
import sail.model as M
import sail.common as S
from sail import initializers


class Target(object):
    def __init__(self, name, index, loss_type, data_flow, run_step, transform_type=None, transform_parameter=None,
                 need_sample_rate=False, loss_weight=1.0, need_serving=True, use_resloss=False):

        self._name = name
        self._index = expand_to_list(index)
        self._loss_type = loss_type
        self._data_flow = expand_to_list(data_flow)
        self._run_step = expand_to_list(run_step)

        self._transform_type = expand_to_list(transform_type)
        self._transform_parameter = expand_to_list(transform_parameter)
        self._need_sample_rate = need_sample_rate
        self._loss_weight = loss_weight
        self._need_serving = need_serving
        self._use_resloss = use_resloss

        if len(self._run_step) != len(self._data_flow) or len(self._index) != len(self._data_flow):
            raise ValueError('The numbers of RUN_STEP and DATA_FLOW are inconsistent')

    def _transform(self, label, idx):
        # distinguish the current transformation method (None default)
        print("Debug Message: transform by ({},{})".format(label, idx))

        transform_type = None
        if isinstance(self._transform_type, list):
            transform_type = self._transform_type[idx]

        # define a specific transformation method
        print("Transform_type: ", transform_type)
        if transform_type is None:
            return label
        elif transform_type == 'norm':
            # if (flow_idx, label_idx) not in self._transform_parameter.keys():
            #     raise ValueError('Transform by norm, but missing parameter with index ({},{})'.format(flow_idx,label_idx))
            mean, lower_bound, upper_bound = self._transform_parameter[idx]
            return tf.clip_by_value(label, lower_bound, upper_bound) / mean
        elif transform_type == 'distill_softmax':
            mean, lower_bound, upper_bound, label_bounds = self._transform_parameter[idx]
            real_label = tf.clip_by_value(label, lower_bound, upper_bound) / mean
            if S.is_training():
                variance = tf.expand_dims(real_var_func(real_label), axis=1) + 1e-6
                real_label_dim = tf.expand_dims(real_label, axis=1)
                probs = real_pdf_func((label_bounds - real_label_dim) / variance)
                probs_norm = tf.reduce_sum(probs, axis=1, keepdims=True)
                label_distill = probs / probs_norm
            else:
                label_distill = tf.placeholder(dtype=M.get_dtype())
            return real_label, label_distill
        elif transform_type == 'softmax':
            mean, lower_bound, upper_bound, label_bounds = self._transform_parameter[idx]
            real_label = tf.clip_by_value(label, lower_bound, upper_bound) / mean
            label_softmax_idx = tf.searchsorted(tf.cast(label_bounds, dtype=real_label.dtype), real_label) - 1 # shift left 1
            label_softmax = tf.one_hot(label_softmax_idx, len(label_bounds)-1)
            return real_label, label_softmax
        else:
            raise ValueError('Unsupportted transform: {}'.format(transform_type))

    def _get_label(self, idx):
        if idx < 0 or idx >= len(self._data_flow):
            raise ValueError('Unsupportted data_flow index: {}'.format(idx))

        return self._data_flow[idx].get_label('label_{}'.format(self._index[idx]))

    @property
    def name(self):
        return self._name

    @property
    def loss_type(self):
        return self._loss_type

    @property
    def run_step(self):
        return self._run_step

    @property
    def need_sample_rate(self):
        return self._need_sample_rate

    @property
    def loss_weight(self):
        return self._loss_weight

    @property
    def need_serving(self):
        return self._need_serving

    @property
    def use_resloss(self):
        return self._use_resloss

    # for train
    def get_label(self, idx):
        label = tf.identity(self._transform(self._get_label(idx), idx), name='target_{}_flow{}'.format(self._name, idx))
        return label

    # for distill train
    def get_distill_label(self, idx):
        real_label, label_distill = self._transform(self._get_label(idx), idx)
        real_label = tf.identity(real_label, name='target_{}_flow{}_real'.format(self._name, idx))
        label_distill = tf.identity(label_distill, name='target_{}_flow{}_distill'.format(self._name, idx))
        return real_label, label_distill

    # for softmax train
    def get_softmax_label(self, idx):
        real_label, label_softmax = self._transform(self._get_label(idx), idx)
        real_label = tf.identity(real_label, name='target_{}_flow{}_real'.format(self._name, idx))
        label_softmax = tf.identity(label_softmax, name='target_{}_flow{}_softmax'.format(self._name, idx))
        return real_label, label_softmax

    # for mask
    def get_other_label(self, idx, label_name_idx):
        label = tf.identity(self._data_flow[idx].get_label('label_{}'.format(label_name_idx)),
                            name='target_{}_flow{}_mask'.format(self._name, idx))
        return label

    # def get_from_two_labels(self, idx):
    #     origin_label = tf.identity(self._data_flow[idx].get_label('label_{}'.format(self._index[idx][0])))
    #     backup_label = tf.identity(self._data_flow[idx].get_label('label_{}'.format(self._index[idx][1])))
    #     zeros = tf.zeros_like(origin_label, dtype=origin_label.dtype)
    #     cond = (origin_label >= zeros)
    #     combine_label = tf.where(cond,origin_label,backup_label)
    #     combine_label = tf.clip_by_value(combine_label, -15.0, 15.0)
    #     return combine_label


def expand_to_list(x):
    if isinstance(x, list):
        return x
    else:
        return [x]

# distill label
def normal_pdf(x, mu=0.0, sigma=1.0):
    # return 1.0 / (sigma * (2.0 * math.pi)**(1/2)) * tf.exp(-1.0 * (x - mu)**2 / (2.0 * (sigma**2)))
    return tf.exp(-1.0 * (x)**2)


def laplace_pdf(x, mu=0.0, sigma=1.0):
    # return 1.0 / (sigma * 2.0) * tf.exp(-1.0 * tf.abs(x - mu) / sigma)
    return tf.exp(-1.0 * tf.abs(x))


def real_pdf_func(x):
    return normal_pdf(x * 10.0, 0.0, 1.0)


def real_var_func(x):
    return 1.5 * tf.pow(x, 0.5)


def relogit(x):
    return tf.clip_by_value(
        tf.math.log(tf.clip_by_value(x, 1e-7, 1)) - tf.math.log(tf.clip_by_value(1 - x, 1e-7, 1)), 
        -15,
        15
    )

def grad_norm(losses, shared_inputs, loss_names=None, scale=1.0, loss_pow=2.0, relative_diff=False):
    """
         final_loss = weighted_loss + gnorm_loss

         ref: GradNorm: Gradient Normalization for Adaptive Loss Balancing in Deep Multitask Networks(https://arxiv.org/abs/1711.02257)
         this implimentation doesn't consider the loss decreasing rate(r) since in recommendation, the loss is not always descending
         @params:
             losses: list of losses without weighting
             shared_inputs: list of weights which are shared
             loss_names: it is used to name the variables in tensorboard
             scale: the weight for grad norm loss
    """
    if len(losses) <= 0:
        raise ValueError('len(losses) must be larger than 1')

    if not loss_names:
        loss_names = [str(i) for i in range(len(losses))]

    if len(loss_names) != len(losses):
        raise ValueError('len(loss_names) != len(losses)')

    if not isinstance(shared_inputs, list):
        shared_inputs = [shared_inputs]

    n = len(losses)

    def get_norm(grad):
        return (tf.reduce_sum(tf.multiply(grad, grad))) ** 0.5

    weights = M.get_variable('grad_norm_weights', [n], initializers.Zeros())
    weights = tf.nn.softmax(weights)

    grads = [tf.gradients(loss, shared_inputs) for loss in losses]
    grads = [tf.concat(gs, axis=1) for gs in grads]

    gnorms = [get_norm(g) for g in grads]
    gnorms = tf.stop_gradient(tf.stack(gnorms, axis=0))

    avgnorm = tf.reduce_sum(gnorms * weights) / n
    wgnorms = gnorms * weights

    # the loss can be very small since the gradients are small,
    # we need to scale it to a larger value to avoid optimization problem
    grad_diff = tf.abs(wgnorms - avgnorm)
    if relative_diff:
        grad_diff = grad_diff / (avgnorm + 1e-6)
    gnorm_loss = tf.reduce_sum(grad_diff ** loss_pow) * scale

    # the weighted loss
    weighted_loss = tf.reduce_sum(tf.stack(losses, axis=0) * tf.stop_gradient(weights))

    for i in range(n):
        tf.summary.scalar('gradnorm/weight_{}'.format(loss_names[i]), weights[i])

    return gnorm_loss, weighted_loss, weights
