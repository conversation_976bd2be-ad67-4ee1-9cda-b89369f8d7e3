# coding:utf-8
import os
import sys
from google.protobuf import text_format
from datetime import datetime, timedelta, date
from byted_euclid_fountain import (Config, HdfsHourlyStorage, DataSet, WallTime, Types, DataFlow)

TO_NOW_HOUR_DELTA = 14

dataset = DataSet()
config = Config(name="batch", dataset=dataset)
config.reader.lagrangex_header = True
config.reader.set_runtime_env("BATCH_SIZE", 256)
config.reader.set_runtime_env("FEATURE_STORE_TASK_RUNNER_NUM", 12)

DataFlow.add_op("neg_sample_by_label", inputs=[Types.PROTO_INSTANCE], outputs=[Types.PROTO_INSTANCE])

batch_data = [
    HdfsHourlyStorage(
        name='q_instance',
        path_prefix='hdfs://harunava/i18n_ad/inst/gmvmax_uplift/pgm_q_instance_v1',
        data_type=Types.PROTO_INSTANCE,
        kafka_dump_prefix=True,
        kafka_dump=False,
        has_sort_id=True,
        start_time=datetime(year=2025, month=5, day=27, hour=1, minute=0, second=0),
        end_time=WallTime(offset_in_seconds=-TO_NOW_HOUR_DELTA*60*60)
    ),
    HdfsHourlyStorage(
        name='r_instance',
        path_prefix='hdfs://harunava/i18n_ad/inst/gmvmax_uplift/pgm_r_instance_v1',
        data_type=Types.PROTO_INSTANCE,
        kafka_dump_prefix=True,
        kafka_dump=False,
        has_sort_id=True,
        start_time=datetime(year=2025, month=5, day=27, hour=1, minute=0, second=0),
        end_time=WallTime(offset_in_seconds=-TO_NOW_HOUR_DELTA*60*60)
    ),
]

data_stream = config.add_data_stream("batch_data", batch_data)

q_instance = data_stream.get_dataflow("q_instance")\
                        .matrix_process()\
                        .filter_by_lineid_field_from_tcc(field_name="app_id",field_op="in",value_key="app_id_list")\
                        .filter_by_invalid_fidv2()\
                        .neg_sample_by_label(label_index='0',sample_rate='0.1',use_instance_sample_rate='1')\
                        .extract_fc_from_line_id(extra_feature_column_fields='external_action,partner_id,creative_type,ori_cvr,ori_ctr')\
                        .shuffle().batch()

r_instance = data_stream.get_dataflow("r_instance")\
                        .matrix_process()\
                        .filter_by_lineid_field_from_tcc(field_name="app_id",field_op="in",value_key="app_id_list")\
                        .filter_by_invalid_fidv2()\
                        .neg_sample_by_label(label_index='0',sample_rate='0.1',use_instance_sample_rate='1')\
                        .extract_fc_from_line_id(extra_feature_column_fields='external_action,partner_id,creative_type,ori_cvr,ori_ctr')\
                        .shuffle().batch()

config.add_fetch(name="q_data", data_flow=q_instance)
config.add_fetch(name="r_data", data_flow=r_instance)