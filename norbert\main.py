import datetime
from fountain_config import config as fountain_config
from fountain_config import dataset as batch_dataset
from norbert.practice import dataio_job


sailor_args = """\
-deep_insight_dump_extra_fields=app_id,pricing,convert_cnt,external_action,is_dpa,advertiser_id,partner_id,is_startup,creative_type,campaign_objective_type,ori_cvr,ori_ctr,country,pt,item_source,analytics_attribution_type,rit,system_origin,optimize_goal,lsa_window_label,dc,trigger_type,window_state,is_false_negative_sample,ecom_card_type
-deep_insight_dump_fids_by_slotids=173,174,87,1
-deep_insight_sample_ratio=0.5
-enable_deep_insight=true
-extra_feature_column_fields=external_action,partner_id,creative_type,ori_cvr,ori_ctr
-file_type=proto
-fountain_perf_mode=true
-has_sort_id=1
-kafka_dump=1
-kafka_dump_prefix=0
-max_watch_number=200000
-metric_start_num=200000
-parallel=4
-print_metric=true
-print_uauc=true
-sample_bias=0
-sleep_seconds_while_starting=1200
-sync_push_flag=true
-tf_cpu_count=8
-tf_inter_op_parallel=6
-tf_intra_op_parallel=8
-uid_mapping_cluster=offline
-uid_type=0
-undefok=enable_deep_insight,deep_insight_sample_ratio
-universal_embedding_list=fc_tiktok_ue_uid_472510:fc_muse_mtl_user_embedding_335016_d8:fc_live_mt_predict_ctr_user_fid_d64_test:fc_live_mt_predict_ctr_author_fid_d64_test:fc_live_mt_predict_staytime_user_fid_d64_test:fc_live_mt_predict_staytime_author_fid_d64_test:fc_live_mt_predict_interaction_user_fid_d64_test:fc_live_mt_predict_interaction_author_fid_d64_test
-universal_embedding_mapping=fc_tiktok_ue_uid_472510:-1:instance,fc_muse_mtl_user_embedding_335016_d8:-1:instance,fc_live_mt_predict_ctr_user_fid_d64_test:-1:instance,fc_live_mt_predict_ctr_author_fid_d64_test:-1:instance,fc_live_mt_predict_staytime_user_fid_d64_test:-1:instance,fc_live_mt_predict_staytime_author_fid_d64_test:-1:instance,fc_live_mt_predict_interaction_user_fid_d64_test:-1:instance,fc_live_mt_predict_interaction_author_fid_d64_test:-1:instance
-use_postfetch_prepush_v2=true
-data_collection_parallel=10
-fountain_discard_unused_feature_parallel=1
-fountain_matrix_process_parallel=4
-fountain_proto_reader_parallel=1
-fountain_shuffle_parallel=8
-fountain_to_instance_batch_parallel=8
-mini_batch_size=256
-training_service_timeout_ms=150000
-training_service_conn_timeout_ms=150000
-dataio_reader_native_enabled=true
"""

batch_stage = {
    'resource_team': 'ads-highway-comm-traffic-k8s',
    'train_mode': 'common',
    'product_type': 'guarantee',
    'vehicle': 'ICEV_PERFORMANCE',  # str, 'EV'：纯潮汐 'PHEV'：潮汐+混布流转 'ICEV'：纯混布
    'dataset': batch_dataset,
    'checkpoint_config': {
        # 'checkpoint_interval': 36000,           # int, 自动snapshot间隔，单位秒，默认不做snapshot
        # 'checkpoint_max_to_keep': None,        # int, 自动snapshot最多保留个数，默认3
        # 'checkpoint_dump_background': None,    # bool, 自动snapshot是否使用dump不停更特性，默认使用
        # 'checkpoint_dump_with_eviction': True  # bool, 自动snapshot是否在dump过程中做特征淘汰，默认不开启
    },
    'roles': [
        {
            'type': 'sailor',
            'prod_name': 'sailor_bin',    # str, sailor_bin/sailor_bin_test
            'prod_version': '0.0.0.0',    # str, scm对应版本号，0.0.0.0代表最新正式版
            'fountain': fountain_config,
            'replicas': 200,             # int, 实例总数
            'cpu_num': 12,               # int, 单个实例分配cpu核数
            'memory_mb': 60000,           # int, 单个实例分配memory数量
            'jvm_memory_mb': 30 * 2 * 1024,
            'gflags': sailor_args,        # str，业务speciafic sailor配置
            'slow_start_interval': 1800,  # int, slow start间隔
        },
        {
            'type': 'ps',
            'ps_type': 'SYMBIOTIC_PS',                     # str, SYMBIOTIC_PS/STABLE_PS，伴生ps/服务ps
                # # 正式版release_note: https://bytedance.feishu.cn/docs/doccnzl9gR6cXSKhshAeIHkS7Rb 负责人: yulele@
            'prod_name': 'data/aml/parameter_server_bin',  # str, 正式版: data/aml/parameter_server_bin 测试版：toutiao/recommend
            'prod_version': '0.0.0.0',                     # str, scm对应版本号，0.0.0.0代表最新正式版
            'replicas': 124,                               # int, 实例总数
            'cpu_num': 2,                                 # int, 单个实例分配cpu核数
            'memory_mb': 14000,                             # int, 单个实例分配memory数量
            'extra_config': {
                'model_config': { # model初始化配置
                    'checkpoint': None,         # str, 需要预加载的model_dump hdfs地址
                    'drop_nn': None,            # bool, 丢弃掉nn
                    'clear_slots': None,        # str, 清理目标slot，';'分隔
                    'clear_grad_slots': None,   # str, 清理目标grad_slot，';'分隔
                    'clear_global_step': True,  # bool, 重置目标grad_step
                    'clear_clear_slots': None,  # bool, clear clear_slots
                    'nn_warmup_config': None,   # dict，nn_warmup配置
                    'evict': True,
                    'no_filter': True,          # bool, 不使用filter，建议开启 see https://bytedance.feishu.cn/wiki/wikcnJZiCNuLbJ6FZ4vhCpGpRPh
                    'use_filter': False
                },
            }
        }
    ],
    'extra_config': {
        "norbert_version": "1.2.*",
        "use_lagrangex_header": True,
        "max_task_num_per_worker": 32,
        "task_success_percent": 100,
        "vip_am": True,
    }
}
catchup_stage = {
    'resource_team': 'ads-highway-comm-traffic-k8s',
    'train_mode': 'catchup_batch',
    'product_type': 'guarantee',
    'vehicle': 'ICEV_PERFORMANCE',  # str, 'EV'：纯潮汐 'PHEV'：潮汐+混布流转 'ICEV'：纯混布
    'dataset': batch_dataset,
    'roles': [
        {
            'type': 'sailor',
            'prod_name': 'sailor_bin',  # str, sailor_bin/sailor_bin_test
            'prod_version': '0.0.0.0',  # str, scm对应版本号，0.0.0.0代表最新正式版
            'fountain': fountain_config,
            'replicas': 128,  # int, 实例总数
            'cpu_num': 8,  # int, 单个实例分配cpu核数
            'memory_mb': 60000,  # int, 单个实例分配memory数量
            'gflags': sailor_args,  # str，业务speciafic sailor配置
            'slow_start_interval': 7200,  # int, slow start间隔
        },
        {
            'type': 'ps',
            'ps_type': 'STABLE_PS',                     # str, SYMBIOTIC_PS/STABLE_PS，伴生ps/服务ps
            'extra_config': {
                'model_config': {
                    'evict': True,
                    'no_filter': True,
                    'use_filter': False
                },
            }
        },
    ],
    'extra_config': {
        'catch_type': 'daily',
        "norbert_version": "1.2.*",
        "use_lagrangex_header": True,
        "max_task_num_per_worker": 32,
        "task_success_percent": 100,
        "vip_am": True,
        "permanent_ps": True,
    }
}

job_config = {
    "stage_config": {
        # "batch_stage": batch_stage,
        "catchup_stage": catchup_stage,
    },
    "stage_order": [
        # {
        #     "default_stage_name": "batch_stage",
        #     "schedule_policy": "once"
        # },
        {
            "default_stage_name": "catchup_stage",
            "schedule_policy": "loop"
        },
    ]
}
