# -*- encoding=utf-8 -*-

import tensorflow as tf
import sail.model as M
from sail import initializers
import sail.common as S
import sail.model as M
from sail import layers as L
from sail import layers
from sail import losses as Losses
from sail import initializers
from sail import optimizers
from sail import modules

from sail.feature import FeatureSlot, FeatureColumnV1, FeatureColumnV2, FeatureColumn3D, FeatureColumnDense

from feature import *

# Constant
FID_V1_MAX_SLOT_ID = 1023
UUE_SCALE_INIT = 0.1

CLIP_MAX_FP16=60000

is_training = S.is_training()

SPARSE_LABEL_RESAMPLE_RATE = 0.3 if is_training else 1.0

# Fuctions
def debug_emb_coverage_summary(embedding, name, need_coverage=True):
    print("debug emb {} shape:".format(name),embedding.shape)
    if len(embedding.shape) == 1:
        embedding_ = tf.reshape(embedding, [-1, 1])
    else:
        embedding_ = embedding
    non_zero_mask = tf.greater(tf.reduce_sum(tf.abs(embedding_), axis=1), 0)
    non_zero_ratio = tf.reduce_mean(tf.cast(non_zero_mask, tf.float32))
    if embedding_.shape[-1] == 1:
        tf.summary.scalar('debug/{}'.format(name), tf.reduce_mean(embedding_))
    # always log histogram
    tf.summary.histogram('debug/{}'.format(name), embedding)
    if need_coverage:
        tf.summary.scalar('debug/non_zero_ratio_{}'.format(name), non_zero_ratio)

def safe_log_sigmoid(logits):
    zeros = tf.zeros_like(logits, dtype=logits.dtype)
    cond = (logits >= zeros)
    relu_logits = tf.where(cond, logits, zeros)
    neg_abs_logits = tf.where(cond, -logits, logits)
    return tf.negative(relu_logits - logits + tf.log1p(tf.exp(neg_abs_logits)))

def get_sample_logits(logits, sample_rate, sample_bias):
    return tf.cond(sample_bias < 1e-6,
                    lambda: tf.add(logits, tf.negative(tf.log(sample_rate))),
                    lambda: tf.add(safe_log_sigmoid(logits), tf.negative(tf.log(sample_rate))))

def get_embedding_input(fc_dict, slots, n, need_concat=True):
    result = []
    for slot in slots:
        if slot > FID_V1_MAX_SLOT_ID:
            embedding = fc_dict[slot].get_vector(fc_dict[slot].feature_slot.add_slice(n))
        else:
            embedding = fc_dict[slot].add_vector(n)
        result.append(embedding)
    if not need_concat:
        return result
    return tf.concat(result, axis=1)

def get_shared_embedding_input(fc_dict, slots, n, slice_dict, need_concat=True):
    result = []
    for slot in slots:
        if slot > FID_V1_MAX_SLOT_ID:
            sidx = slice_dict[slot] if slot in slice_dict.keys() else 0
            embedding = fc_dict[slot].get_vector(fc_dict[slot].feature_slot.feature_slices[sidx])
        else:
            embedding = fc_dict[slot].add_vector(n)
        result.append(embedding)
    if not need_concat:
        return result
    return tf.concat(result, axis=1)


def loss_reweight(loss, label, logit):
    logit_stop = tf.stop_gradient(logit)
    # weight = tf.where(label>1,tf.pow(label,0.1),tf.ones_like(label))
    scale = tf.where(logit_stop>label, tf.divide(logit_stop,label+0.01), tf.divide(label,logit_stop+0.01))
    scale = tf.clip_by_value(tf.where(tf.abs(logit_stop-label)<0.01*tf.ones_like(label), tf.ones_like(label), scale) , 1, 2)
    # scale = tf.clip_by_value(1+tf.abs(tf.divide(logit_stop,label)-1),1,2)
    weight = tf.where(label>1,tf.pow(label,0.1),tf.ones_like(label)) * scale
    return tf.multiply(loss,weight)

def get_rank_loss(label, pred, mask=None):
    if mask is None:
        mask = tf.ones_like(label, dtype=tf.bool)
    valid_indices = tf.boolean_mask(tf.range(tf.size(label)), mask)
    ori_label = tf.gather(label, valid_indices)
    ori_preds = tf.gather(pred, valid_indices)

    sampled_indices = tf.gather(valid_indices, tf.random.shuffle(tf.range(tf.size(valid_indices))))
    sampled_label = tf.gather(label, sampled_indices)
    sampled_preds = tf.gather(pred, sampled_indices)

    update_first = tf.less(tf.abs(tf.divide(ori_label,ori_preds)-1), tf.abs(tf.divide(sampled_label,sampled_preds)-1))
    ori_preds = tf.where(update_first, tf.stop_gradient(ori_preds), ori_preds)
    sampled_preds = tf.where(update_first, sampled_preds, tf.stop_gradient(sampled_preds))

    ratio_labels = tf.clip_by_value(tf.divide(ori_label, (sampled_label + 1e-8)),1e-2,100)
    ratio_preds = tf.clip_by_value(tf.divide(ori_preds, (sampled_preds + 1e-8)),1e-2,100)

    loss = tf.square(ratio_labels - ratio_preds)
    rank_mask = tf.where( (ori_label-sampled_label)*(ori_preds-sampled_label)<tf.zeros_like(ori_label), tf.ones_like(ori_label), tf.zeros_like(ori_label))
    loss = rank_mask * loss

    return loss,rank_mask



def get_loss(label, logit, loss_type='logloss', mask=None, need_reweight=False):
    # calc loss
    if loss_type == 'logloss':
        loss = tf.nn.sigmoid_cross_entropy_with_logits(labels=label, logits=logit)
    elif loss_type == 'huberloss':
        loss = tf.losses.huber_loss(labels=label, predictions=logit, delta=1.0, reduction='none')
    elif loss_type == 'mseloss':
        loss = tf.losses.mean_squared_error(labels=label, predictions=logit, reduction='none')
    elif loss_type == 'mapeloos':
        loss = tf.abs(tf.divide(tf.subtract(logit,label),(label + 1e-10)))
    elif loss_type == 'wceloss':
        loss = -label * logit + (label + 1) * tf.log(1 + tf.exp(logit))
        # loss = -label * logit + (1 + label) * (tf.log(1 + tf.exp(-abs(logit))) + tf.maximum(logit, 0))
    elif loss_type == 'softmax_v1':
        loss = tf.nn.sparse_softmax_cross_entropy_with_logits(labels=label, logits=logit) 
    elif loss_type == 'distill_softmax':
        label, distill_label = label
        loss = tf.nn.softmax_cross_entropy_with_logits_v2(labels=distill_label, logits=logit)
    elif loss_type == 'softmax':
        label, softmax_label = label
        loss = tf.nn.softmax_cross_entropy_with_logits_v2(labels=softmax_label, logits=logit)
    else:
        raise ValueError('Unsupportted Loss Type: {}'.format(loss_type))
    # calc mask if need
    if mask is None:
        # set defalut mask
        mask = tf.ones_like(label, dtype=tf.bool)
    if loss_type not in ['logloss','softmax']:
        # mask for norm label
        zeros = tf.zeros_like(label, dtype=label.dtype)
        cond = (label >= zeros)
        mask = mask & cond
    if need_reweight:
        loss = loss_reweight(loss,label,logit)
    loss = tf.boolean_mask(loss, mask)
    return loss, mask

# dense fea functions
def get_embedding(name,embedding_id,dim,bucket_num):
    embedding = M.get_variable(name, [bucket_num+2, dim],
            initializer=initializers.RandomUniform(-0.01, 0.01),
            trainable=True)
    # with tf.control_dependencies(tf.get_collection(tf.GraphKeys.UPDATE_OPS)):
    #     embedding_id = tf.Print(embedding_id, [embedding_id ], message='dense_feature/{}'.format(name), summarize=-1)
    tf.summary.histogram('dense_feature/{}'.format(name), embedding_id)
    return tf.nn.embedding_lookup(embedding, embedding_id)

def div2bucket(dense_scalar, lower_bound, upper_bound, use_log=False, bucket_num=10, smooth=1e-3):
    if use_log: 
        dense_scalar = tf.log(dense_scalar + 1.0)/tf.cast(tf.log(1.1), dense_scalar.dtype)
        lower_bound = tf.log(lower_bound + 1.0 + smooth)/tf.log(1.1)
        upper_bound = tf.log(upper_bound + 1.0)/tf.log(1.1)
    lower_bound = tf.cast(lower_bound, dense_scalar.dtype)
    upper_bound = tf.cast(upper_bound, dense_scalar.dtype)
    dense_scalar = tf.cast(tf.ceil(tf.div(dense_scalar - lower_bound, upper_bound - lower_bound) * bucket_num), tf.int32)
    return tf.maximum(tf.minimum(dense_scalar, bucket_num+1), 0)

def dense_dropout(fc_dense_embedding,name,dropout_zero_ratio=0.05,dropout_rand_ratio=0.05,dropout_rand_scale=0.5):
    bs = tf.shape(fc_dense_embedding)[0]
    rand = tf.random_uniform(shape=[bs], dtype=M.get_dtype())
    fc_dense_embedding = tf.where(tf.less(rand, tf.fill([bs], tf.constant(dropout_zero_ratio, dtype=M.get_dtype()))),
                            tf.zeros_like(fc_dense_embedding),
                            tf.where(tf.less(rand, tf.fill([bs],tf.constant(dropout_zero_ratio + dropout_rand_ratio,dtype=M.get_dtype()))),
                                    tf.random_uniform(shape=tf.shape(fc_dense_embedding), minval=-dropout_rand_scale,
                                                    maxval=dropout_rand_scale, dtype=M.get_dtype()),
                                    fc_dense_embedding))
    tf.summary.histogram('dense/{}_dropout_dense_feature'.format(name), fc_dense_embedding)
    return fc_dense_embedding

def get_dense_list_feature(dense_name,fc_config,dim,input_tf_type=tf.float64,is_dense_list=False):
    dense_feature_list = []
    for name in fc_config:
        max_size, lower_bound, upper_bound, fun, bucket_num = fc_config[name]
        dense_feature = tf.cast(FeatureColumnDense(name, max_size, input_tf_type).get_tensor(), tf.float64)
        if is_dense_list and max_size>1:
            dense_feature = tf.reduce_sum(dense_feature,axis=1)
        tf.summary.histogram('dense_feature_{}_ori/{}'.format(dense_name,name), dense_feature)
        dense = tf.reshape(dense_feature,[-1])
        f_id = div2bucket(dense, lower_bound, upper_bound, fun == 'log', bucket_num)
        fc_dense_embedding = get_embedding("dense/"+name,f_id,dim,bucket_num)
        dense_feature_list.append(fc_dense_embedding)
    print("{} dense feature nums: {}".format(dense_name, len(dense_feature_list)))
    dense_feature_list = tf.concat(dense_feature_list, axis=1)
    print("{} dense feature nums: {}".format(dense_name, dense_feature_list))
    return dense_feature_list

# Layers
def mlp(layer_input, weight, bias):
    layer_output = tf.matmul(layer_input, weight) + bias
    print("layer_output", layer_output.shape)
    return layer_output


def get_fc_3d(slot, fc_name_dict, seq_len, dim):
    fs = FeatureSlot(slot_id=slot,
                     vec_optimizer=optimizers.FTRLWithGroupSparsity(alpha=.025, 
                                                                        beta=1.0, 
                                                                        init_factor=0.0001,
                                                                        lambda1=1,
                                                                        lambda2=1,
                                                                        zero_val_expire_time=3),
                     bias_optimizer=optimizers.FTRL(alpha=0.01, beta=1.0, lambda1=1.0, lambda2=1.0),
                     occurrence_threshold=0)
    fc = FeatureColumn3D(fc_name_dict[slot], seq_len, fs)

    emb = fc.get_vector(fc.feature_slot.add_slice(dim))
    mask = fc.get_size_tensor()
    return emb, mask

def get_shared_fc_3d(fc_dict, slot, fc_name_dict, seq_len, dim, slice_index=0):
    # slot已被记录
    if slot in fc_dict.keys():
        # 取出已经创建的feature slot
        fs = fc_dict[slot].feature_slot
        # 根据fs创建新的feature column
        fc = FeatureColumn3D(fc_name_dict[slot], seq_len, fs)
        # 确定需要share embedding的特征是哪段，默认可选一定存在的第0个
        emb = fc.get_vector(fs.feature_slices[slice_index])
        mask = fc.get_size_tensor()
        last_dim = fs.feature_slices[slice_index].len
    else:
        fs = FeatureSlot(slot_id=slot,
                    vec_optimizer=optimizers.FTRLWithGroupSparsity(alpha=.025, 
                                                                        beta=1.0, 
                                                                        init_factor=0.0001,
                                                                        lambda1=1,
                                                                        lambda2=1,
                                                                        zero_val_expire_time=3),
                    bias_optimizer=optimizers.FTRL(alpha=0.01, beta=1.0, lambda1=1.0, lambda2=1.0),
                    occurrence_threshold=0)
        fc = FeatureColumn3D(fc_name_dict[slot], seq_len, fs)
        # 将未被记录的保存到fc_dict
        fc_dict[slot] = fc
        emb = fc.get_vector(fc.feature_slot.add_slice(dim))
        mask = fc.get_size_tensor()
        last_dim = dim
    return emb, mask, last_dim


def attention_cross(queries, keys, profiles, masks):
    """
        queries:[bs, 32*16+16+16*16+16]
    """
    print("queries", queries.shape)
    keys = tf.add_n(keys)  # [bs, seq_len, emb_size=32]
    print("keys", keys.shape)
    profile_len = len(profiles)
    if profile_len>0:
        profiles = tf.add_n(profiles)  # [bs, seq_len, emb_size=16]
        print("profiles", profiles.shape)
    else:
        print("empty profiles")

    mask = masks[0]  # [bs, seq_len]

    layer_weight_1 = tf.reshape(tf.slice(queries, [0, 0], [-1, 32 * 16]), [-1, 32, 16])
    # print("layer_weight_1", layer_weight_1.shape)
    layer_bias_1 = tf.reshape(tf.slice(queries, [0, 32 * 16], [-1, 16]), [-1, 1, 16])
    print("layer_bias_1", layer_bias_1.shape)
    layer_weight_2 = tf.reshape(tf.slice(queries, [0, 32 * 16 + 16], [-1, 16 * 16]), [-1, 16, 16])
    print("layer_weight_2", layer_weight_2.shape)
    layer_bias_2 = tf.reshape(tf.slice(queries, [0, 32 * 16 + 16 + 16 * 16], [-1, 16]), [-1, 1, 16])
    print("layer_bias_2", layer_bias_2.shape)

    layer_output = tf.nn.tanh(mlp(keys, layer_weight_1, layer_bias_1))  # [bs, seq_len, 16]
    layer_output = mlp(layer_output, layer_weight_2, layer_bias_2)  # [bs, seq_len, 16]
    outputs = layer_output

    # Mask
    # raw_mask = mask
    mask = tf.equal(mask, tf.ones_like(mask))  # [bs, seq_len] 取值为True or False
    key_masks = tf.expand_dims(mask, 2)  # [bs, seq_len, 1]
    # mask = tf.expand_dims(raw_mask, 2)

    keys_masks = tf.cast(tf.where(key_masks, tf.ones_like(key_masks), tf.zeros_like(key_masks)), dtype=outputs.dtype)
    print("keys_masks", keys_masks.shape)

    # Weighted sum
    if profile_len>0:
        outputs = tf.multiply(outputs, profiles)
    outputs = tf.reduce_sum(tf.multiply(outputs, keys_masks), axis=1)  # [bs, 16]
    print("outputs:", outputs.shape)

    tf.summary.histogram("can_output", outputs)
    return outputs


def do_seq(fc_dict, fc_name_dict, target_out, SEQ_ID_slots, SEQ_PROFILE_slots, seq_len):
    masks = []
    seq_id = []
    seq_profile = []
    print("======== SEQ CAN CODE ======")
    for slot in SEQ_ID_slots:
        # xd_tensor:[bs, seq_len, emd_size]  mask:[bs, seq_len]
        slot_dim = 32
        xd_tensor, mask, last_dim = get_shared_fc_3d(fc_dict, slot, fc_name_dict, seq_len, slot_dim, 0)
        if last_dim!=slot_dim:
            print("[SEQ_ID] Share Embedding dim = {} Reshape To {}".format(last_dim,slot_dim))
            feature_reshape_weight = M.get_variable(name = "CAN_{}_seqid_slot_{}_reshape".format(fc_name_dict[slot],slot),
                                                    shape=[last_dim,slot_dim], initializer=initializers.VarianceScaling())
            xd_tensor = tf.matmul(xd_tensor,feature_reshape_weight)
        tf.summary.histogram("seq_slot_%s" % slot, xd_tensor)
        tf.summary.histogram("seq_slot_%s_size" % slot, mask)
        mask = tf.clip_by_value(mask, 0, 1)
        tf.summary.histogram("seq_slot_%s_length" % slot, tf.reduce_sum(mask, axis=1))
        seq_id.append(xd_tensor)
        masks.append(mask)
    for slot in SEQ_PROFILE_slots:
        slot_dim = 16
        xd_tensor, mask, last_dim = get_shared_fc_3d(fc_dict, slot, fc_name_dict, seq_len, slot_dim, 0)
        if last_dim!=slot_dim:
            print("[SEQ_PROFILE] Share Embedding dim = {} Reshape To {}".format(last_dim,slot_dim))
            feature_reshape_weight = M.get_variable(name = "CAN_{}_seqid_slot_{}_reshape".format(fc_name_dict[slot],slot),
                                                    shape=[last_dim,slot_dim], initializer=initializers.VarianceScaling())
            xd_tensor = tf.matmul(xd_tensor,feature_reshape_weight)
        tf.summary.histogram("seq_slot_%s" % slot, xd_tensor)
        tf.summary.histogram("seq_slot_%s_size" % slot, mask)
        mask = tf.clip_by_value(mask, 0, 1)
        tf.summary.histogram("seq_slot_%s_length" % slot, tf.reduce_sum(mask, axis=1))
        seq_profile.append(xd_tensor)
    # seq: do attention
    seq_output = attention_cross(target_out, seq_id, seq_profile, masks)
    print("can_seq_output shape", seq_output.shape)
    return seq_output



def target_attention(queries, keys, values, masks, seq_len, query_emb_dim, key_emb_dim, value_emb_dim, attention_emb_dim, name):
    """
        queries:[[bs, emb_size], ...]
        keys/values: [[bs, seq_len, emb_size], ...]
    """
    query = tf.concat(queries, axis=1) #[bs, query_emb_dim]
    wq = M.get_variable("TargetAttention_{}_wq".format(name), [query_emb_dim,attention_emb_dim], initializer=initializers.VarianceScaling())
    query = tf.matmul(query,wq)
    print("query:", query.shape)     #[bs, attention_emb_dim]
    key = tf.concat(keys, axis=2)      #[bs, seq_len, key_emb_dim]
    wk = M.get_variable("TargetAttention_{}_wk".format(name), [key_emb_dim,attention_emb_dim], initializer=initializers.VarianceScaling())
    key = tf.matmul(key,wk)
    print("key:", key.shape)          #[bs, seq_len, attention_emb_dim]
    value = tf.concat(values, axis=2)    #[bs, seq_len, value_emb_dim]
    print("value:", value.shape)
    mask = masks[0]  # [bs, seq_len]

    query = tf.expand_dims(query, axis=1)    #[bs, 1, attention_emb_dim]
    weight = tf.matmul(query,tf.transpose(key, [0,2,1]))   #[bs, 1, seq_len]
    print("weight:", weight.shape)
    weight = tf.nn.sigmoid(weight)
    weight = tf.transpose(tf.tile(weight, [1,value_emb_dim,1]), [0,2,1])  #[bs, seq_len, value_emb_dim]
    outputs = tf.multiply(weight, value) #[bs, seq_len, value_emb_dim]
    print("outputs:", outputs.shape)

    # raw_mask = mask
    mask = tf.equal(mask, tf.ones_like(mask))  # [bs, seq_len] 取值为True or False
    key_masks = tf.expand_dims(mask, 2)  # [bs, seq_len, 1]
    # mask = tf.expand_dims(raw_mask, 2)
    keys_masks = tf.cast(tf.where(key_masks, tf.ones_like(key_masks), tf.zeros_like(key_masks)), dtype=outputs.dtype)
    print("keys_masks", keys_masks.shape)

    outputs = tf.reduce_sum(tf.multiply(outputs, keys_masks), axis=1)  # [bs, value_emb_dim]
    print("outputs:", outputs.shape)
    tf.summary.histogram("din_output", outputs, family=name)

    return outputs


def do_seq_din(fc_dict, query, Key_slots, Value_Slots, seq_len, slot_dim, query_emb_dim, attention_emb_dim, name):
    masks = []
    keys = []
    values = []
    print("======== SEQ DIN CODE ======")
    for slot in Key_slots:
        # xd_tensor:[bs, seq_len, emd_size]  mask:[bs, seq_len]
        xd_tensor, mask, last_dim = get_shared_fc_3d(fc_dict, slot, ROOM_SEQ_FC_NAME, seq_len, slot_dim, slice_index=0)
        # share embedding的维度与目标的获取维度dim不一致时，线性映射
        if last_dim!=slot_dim:
            print("[KEY] Share Embedding dim = {} Reshape To {}".format(last_dim,slot_dim))
            feature_reshape_weight = M.get_variable(name = "TargetAttention_{}_KEY_slot_{}_reshape".format(name,slot),
                                                    shape=[last_dim,slot_dim], initializer=initializers.VarianceScaling())
            xd_tensor = tf.matmul(xd_tensor,feature_reshape_weight)
        tf.summary.histogram("key_slot_%s" % slot, xd_tensor, family=name)
        tf.summary.histogram("key_slot_%s_size" % slot, mask, family=name)
        mask = tf.clip_by_value(mask, 0, 1)
        tf.summary.histogram("key_slot_%s_length" % slot, tf.reduce_sum(mask, axis=1), family=name)
        keys.append(xd_tensor)
        masks.append(mask)
    if Value_Slots is None or len(Value_Slots)==0:
        values = keys
    else:
        for slot in Value_Slots:
            # xd_tensor:[bs, seq_len, emd_size]  mask:[bs, seq_len]
            xd_tensor, mask, last_dim = get_shared_fc_3d(fc_dict, slot, ROOM_SEQ_FC_NAME, seq_len, slot_dim, slice_index=0)
            if last_dim!=slot_dim:
                print("[VALUE] Share Embedding dim = {} Reshape To {}".format(last_dim,slot_dim))
                feature_reshape_weight = M.get_variable(name = "TargetAttention_{}_Value_slot_{}_reshape".format(name,slot),
                                                        shape=[last_dim,slot_dim], initializer=initializers.VarianceScaling())
                xd_tensor = tf.matmul(xd_tensor,feature_reshape_weight)
            tf.summary.histogram("value_slot_%s" % slot, xd_tensor, family=name)
            tf.summary.histogram("value_slot_%s_size" % slot, mask, family=name)
            mask = tf.clip_by_value(mask, 0, 1)
            tf.summary.histogram("value_slot_%s_length" % slot, tf.reduce_sum(mask, axis=1), family=name)
            values.append(xd_tensor)
    seq_output = target_attention(query, keys, values, masks, seq_len, query_emb_dim, slot_dim*len(keys), slot_dim*len(values), attention_emb_dim, name)
    print("din seq_output shape", seq_output.shape)
    return seq_output


def sum_pooling(fc_dict, input_map, features, dim, total_embeddings, add_into_embeddings=True):
    slot_embeddings = []
    dims = 0
    for slot in features:
        # allocate embedding
        if slot > FID_V1_MAX_SLOT_ID:
            embedding = fc_dict[slot].get_vector(fc_dict[slot].feature_slot.add_slice(dim))
        else:
            embedding = fc_dict[slot].add_vector(dim)
        dims += dim
        if add_into_embeddings:
            total_embeddings.append((embedding, dim))
        slot_embeddings.append(embedding)
        if slot in input_map:
            input_slots = input_map.keys()
            c = 0
            for item in input_slots:
                if isinstance(item, str):
                    if str(slot) + '_' in item:
                        c += 1
                if isinstance(item, int):
                    if item == slot:
                        c += 1
            input_map[str(slot) + '_' + str(c)] = embedding
        else:
            input_map[slot] = embedding
    if len(features) == 1:  # 单特征无需sum
        return slot_embeddings[0]
    return tf.add_n(slot_embeddings)

def remove_nan_and_clip(x, clip_value_min=None, clip_value_max=None, default_nan_value=None):
    default_value = (tf.ones_like(x) * default_nan_value) if (default_nan_value is not None) else tf.zeros_like(x)
    x = tf.where(tf.logical_or(tf.is_nan(x), tf.is_inf(x)), default_value, x)
    if (clip_value_min is None) or (clip_value_max is None):
        return x
    else:
        return tf.clip_by_value(x, clip_value_min, clip_value_max)

def gen_ue(fc_name, fc_dim, fc_dict, input_map, total_embeddings, fm_outputs, fms,
            need_dropout=False, dropout_zero_ratio=0.05, dropout_rand_ratio=0.05, dropout_rand_scale=0.5,
            need_compress=False, compress_dims=[128, 64],
            need_fm=False, dense_cross_init_weight=0.1, fm_cross_slot=[2],
            need_concat_nn=False, need_compress_from_raw=False):
    # alloc fc
    fc_dense = FeatureColumnDense(fc_name, fc_dim)
    fc_dense = fc_dense.get_tensor()
    fc_dense = remove_nan_and_clip(fc_dense, clip_value_min=-CLIP_MAX_FP16, clip_value_max=CLIP_MAX_FP16)
    fc_raw_dense = fc_dense
    tf.summary.histogram('{}_raw_dense'.format(fc_name), fc_dense)
    # dropout
    if is_training and need_dropout:
        bs = tf.shape(fc_dense)[0]
        rand = tf.random_uniform(shape=[bs], dtype=M.get_dtype())
        fc_dense = tf.where(tf.less(rand, tf.fill([bs], tf.constant(dropout_zero_ratio, dtype=M.get_dtype()))),
                            tf.zeros_like(fc_dense),
                            tf.where(tf.less(rand, tf.fill([bs],
                                                            tf.constant(dropout_zero_ratio + dropout_rand_ratio,
                                                                        dtype=M.get_dtype()))),
                                        tf.random_uniform(shape=tf.shape(fc_dense), minval=-dropout_rand_scale,
                                                        maxval=dropout_rand_scale, dtype=M.get_dtype()),
                                        fc_dense))
        tf.summary.histogram('{}_dropout_dense'.format(fc_name), fc_dense)
    # compress
    if need_compress:
        prod_compress_tower = modules.DenseTower(name='{}_compress_tower'.format(fc_name),
                                                    output_dims=compress_dims,
                                                    initializers=initializers.GlorotNormal(mode='fan_in'))
        fc_dense = prod_compress_tower(fc_dense)
        tf.summary.histogram('{}_compressed'.format(fc_name), fc_dense)
    # fm
    if need_fm:
        input_dim = compress_dims[-1] if need_compress else fc_dim
        uue_group_pooling = sum_pooling(fc_dict, input_map, fm_cross_slot, input_dim, total_embeddings, False)
        uue_prod = tf.multiply(fc_dense, uue_group_pooling)
        uue_scale_dot = M.get_variable(name='uue_scale_dot', shape=[1],
                                        initializer=initializers.Ones()) * UUE_SCALE_INIT
        uue_scale_prod = M.get_variable(name='uue_scale_prod', shape=[1],
                                        initializer=initializers.Ones()) * UUE_SCALE_INIT
        uue_dot = tf.reduce_sum(uue_prod, axis=1)

        fm_outputs.append(uue_dot * uue_scale_dot)
        fms.append(uue_prod * uue_scale_prod)
    # concat to nn
    if need_concat_nn:
        if need_compress_from_raw:
            concat_compress_tower = modules.DenseTower(
                name='{}_concat_compress_tower'.format(fc_name),
                output_dims=compress_dims,
                initializers=initializers.GlorotNormal(mode='fan_in')
            )
            fc_concat = concat_compress_tower(fc_raw_dense)
            fc_scale_concat = M.get_variable(name='{}_scale_concat'.format(fc_name), shape=[1],
                                                initializer=initializers.Ones()) * UUE_SCALE_INIT
            input_dim = compress_dims[-1]
            total_embeddings.append((fc_concat * fc_scale_concat, input_dim))
            tf.summary.histogram('{}_uue_concat'.format(fc_name), fc_concat)
            tf.summary.histogram('{}_uue_scale_concat'.format(fc_name), fc_scale_concat)
        else:
            input_dim = compress_dims[-1] if need_compress or need_compress_from_raw else fc_dim
            total_embeddings.append((fc_dense, input_dim))
            tf.summary.histogram('{}_nn_concat_input_dense'.format(fc_name), fc_dense)
    return fc_dense